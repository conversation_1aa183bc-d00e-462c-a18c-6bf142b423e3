<?php

namespace App\Services;

use App\Models\Account;
use App\Models\ClassEnrollment;
use App\Models\Student;
use App\Models\StudentEnrollment;
use App\Models\StudentTransaction;
use App\Models\StudentTuition;
use App\Models\SubjectEnrollment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

final class StudentIdUpdateService
{
    /**
     * Update a student's ID and all related records
     *
     * @param Student $student The student to update
     * @param int $newId The new ID to assign
     * @param bool $bypassSafetyChecks Whether to bypass safety checks (when user has confirmed)
     * @return array Result array with success status and message
     */
    public function updateStudentId(Student $student, int $newId, bool $bypassSafetyChecks = false): array
    {
        // Comprehensive validation
        $validation = $this->validateNewId($student, $newId);
        if ($validation !== true) {
            return $validation;
        }

        // Additional safety checks (only if not bypassed)
        if (!$bypassSafetyChecks) {
            $safetyCheck = $this->performSafetyChecks($student, $newId);
            if ($safetyCheck !== true) {
                return $safetyCheck;
            }
        }

        $oldId = $student->id;

        // Pre-update verification
        $preUpdateVerification = $this->verifyPreUpdateState($student);
        if (!$preUpdateVerification['success']) {
            return $preUpdateVerification;
        }

        DB::beginTransaction();

        try {
            // Update all related records first (before updating the student)
            $updateResults = $this->updateRelatedRecords($oldId, $newId);

            // Update the student record itself
            $student->id = $newId;
            $student->save();

            // Post-update verification
            $postUpdateVerification = $this->verifyPostUpdateState($oldId, $newId);
            if (!$postUpdateVerification['success']) {
                DB::rollBack();
                return $postUpdateVerification;
            }

            DB::commit();

            Log::info("Student ID updated successfully", [
                'old_id' => $oldId,
                'new_id' => $newId,
                'student_name' => $student->full_name,
                'updated_records' => $updateResults
            ]);

            return [
                'success' => true,
                'message' => "Student ID successfully updated from {$oldId} to {$newId}. Updated {$updateResults['total_updated']} related records.",
                'old_id' => $oldId,
                'new_id' => $newId,
                'updated_records' => $updateResults
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error("Failed to update student ID", [
                'old_id' => $oldId,
                'new_id' => $newId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => "Failed to update student ID: " . $e->getMessage(),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Validate the new ID
     *
     * @param Student $student
     * @param int $newId
     * @return array|true
     */
    private function validateNewId(Student $student, int $newId): array|true
    {
        // Check if new ID is the same as current
        if ($student->id === $newId) {
            return [
                'success' => false,
                'message' => 'New ID cannot be the same as the current ID'
            ];
        }

        // Check if new ID already exists
        if (Student::where('id', $newId)->exists()) {
            return [
                'success' => false,
                'message' => "Student ID {$newId} already exists"
            ];
        }

        // Validate ID format (assuming positive integers)
        if ($newId <= 0) {
            return [
                'success' => false,
                'message' => 'Student ID must be a positive integer'
            ];
        }

        return true;
    }

    /**
     * Perform additional safety checks before updating
     *
     * @param Student $student
     * @param int $newId
     * @return array|true
     */
    private function performSafetyChecks(Student $student, int $newId): array|true
    {
        // Check if student has active enrollments
        $activeEnrollments = $student->subjectEnrolled()->count();
        if ($activeEnrollments > 5) {
            return [
                'success' => false,
                'message' => "Student has {$activeEnrollments} active enrollments. This is a high-risk operation. Please confirm in the form to proceed."
            ];
        }

        // Check if student has recent transactions
        $recentTransactions = $student->StudentTransactions()
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        if ($recentTransactions > 3) {
            return [
                'success' => false,
                'message' => "Student has {$recentTransactions} recent transactions. Changing ID may affect financial records. Please confirm in the form to proceed."
            ];
        }

        // Check if the new ID follows institutional patterns
        if ($newId < 100000 || $newId > 9999999) {
            return [
                'success' => false,
                'message' => "New ID {$newId} doesn't follow institutional ID patterns (should be 6-7 digits). Please verify this is correct."
            ];
        }

        return true;
    }

    /**
     * Verify the state before updating
     *
     * @param Student $student
     * @return array
     */
    private function verifyPreUpdateState(Student $student): array
    {
        // Verify student exists and is accessible
        if (!$student->exists) {
            return [
                'success' => false,
                'message' => 'Student record does not exist or is not accessible.'
            ];
        }

        // Verify student is not soft deleted
        if ($student->trashed()) {
            return [
                'success' => false,
                'message' => 'Cannot update ID of a deleted student record.'
            ];
        }

        return [
            'success' => true,
            'message' => 'Pre-update verification passed.'
        ];
    }

    /**
     * Verify the state after updating
     *
     * @param int $oldId
     * @param int $newId
     * @return array
     */
    private function verifyPostUpdateState(int $oldId, int $newId): array
    {
        // Verify the student record was updated
        $updatedStudent = Student::find($newId);
        if (!$updatedStudent) {
            return [
                'success' => false,
                'message' => 'Student record with new ID was not found after update.'
            ];
        }

        // Verify old ID no longer exists
        $oldStudent = Student::find($oldId);
        if ($oldStudent) {
            return [
                'success' => false,
                'message' => 'Old student ID still exists after update. Data integrity compromised.'
            ];
        }

        // Verify related records were updated
        $remainingOldReferences = $this->getAffectedRecordsSummary($oldId);
        $totalOldReferences = array_sum($remainingOldReferences);

        if ($totalOldReferences > 0) {
            return [
                'success' => false,
                'message' => "Found {$totalOldReferences} records still referencing old ID {$oldId}. Update incomplete."
            ];
        }

        return [
            'success' => true,
            'message' => 'Post-update verification passed.'
        ];
    }

    /**
     * Update all records that reference the student ID
     *
     * @param int $oldId
     * @param int $newId
     * @return array
     */
    private function updateRelatedRecords(int $oldId, int $newId): array
    {
        $results = [];

        // Update StudentTuition records
        $results['student_tuitions'] = StudentTuition::where('student_id', $oldId)->update(['student_id' => $newId]);

        // Update StudentTransaction records
        $results['student_transactions'] = StudentTransaction::where('student_id', $oldId)->update(['student_id' => $newId]);

        // Update StudentEnrollment records
        $results['student_enrollments'] = StudentEnrollment::where('student_id', $oldId)->update(['student_id' => $newId]);

        // Update ClassEnrollment records
        $results['class_enrollments'] = ClassEnrollment::where('student_id', $oldId)->update(['student_id' => $newId]);

        // Update SubjectEnrollment records
        $results['subject_enrollments'] = SubjectEnrollment::where('student_id', $oldId)->update(['student_id' => $newId]);

        // Update Account records (if they reference student by person_id)
        $results['accounts'] = Account::where('person_id', $oldId)->update(['person_id' => $newId]);

        $results['total_updated'] = array_sum($results);

        return $results;
    }

    /**
     * Get a summary of records that will be affected by the ID change
     *
     * @param int|string $studentId
     * @return array
     */
    public function getAffectedRecordsSummary(int|string $studentId): array
    {
        // Convert to integer if it's a valid numeric string
        if (is_string($studentId)) {
            if (!is_numeric($studentId)) {
                return []; // Invalid ID format, return empty array
            }
            $studentId = (int) $studentId;
        }

        return [
            'student_tuitions' => StudentTuition::where('student_id', $studentId)->count(),
            'student_transactions' => StudentTransaction::where('student_id', $studentId)->count(),
            'student_enrollments' => StudentEnrollment::where('student_id', $studentId)->count(),
            'class_enrollments' => ClassEnrollment::where('student_id', $studentId)->count(),
            'subject_enrollments' => SubjectEnrollment::where('student_id', $studentId)->count(),
            'accounts' => Account::where('person_id', $studentId)->count(),
        ];
    }

    /**
     * Check if a student ID is available
     *
     * @param int|string $id
     * @return bool
     */
    public function isIdAvailable(int|string $id): bool
    {
        // Convert to integer if it's a valid numeric string
        if (is_string($id)) {
            if (!is_numeric($id)) {
                return false; // Invalid ID format
            }
            $id = (int) $id;
        }

        return !Student::where('id', $id)->exists();
    }

    /**
     * Generate a suggested new ID based on existing patterns
     *
     * @return int
     */
    public function generateSuggestedId(): int
    {
        // Get the highest existing ID and add 1
        $maxId = Student::max('id') ?? 0;
        return $maxId + 1;
    }

    /**
     * Perform a dry run to check what would be updated
     *
     * @param Student $student
     * @param int $newId
     * @param bool $bypassSafetyChecks Whether to bypass safety checks
     * @return array
     */
    public function dryRun(Student $student, int $newId, bool $bypassSafetyChecks = false): array
    {
        $validation = $this->validateNewId($student, $newId);
        if ($validation !== true) {
            return $validation;
        }

        // Check safety only if not bypassed
        if (!$bypassSafetyChecks) {
            $safetyCheck = $this->performSafetyChecks($student, $newId);
            if ($safetyCheck !== true) {
                return $safetyCheck;
            }
        }

        $summary = $this->getAffectedRecordsSummary($student->id);
        $totalRecords = array_sum($summary);

        return [
            'success' => true,
            'message' => "Dry run successful. {$totalRecords} records would be updated.",
            'affected_records' => $summary,
            'total_records' => $totalRecords,
            'old_id' => $student->id,
            'new_id' => $newId
        ];
    }
}
