<?php

namespace App\Services;

use App\Models\Account;
use App\Models\ClassEnrollment;
use App\Models\Student;
use App\Models\StudentEnrollment;
use App\Models\StudentTransaction;
use App\Models\StudentTuition;
use App\Models\SubjectEnrollment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

final class StudentIdUpdateService
{
    /**
     * Update a student's ID and all related records
     *
     * @param Student $student The student to update
     * @param int $newId The new ID to assign
     * @return array Result array with success status and message
     */
    public function updateStudentId(Student $student, int $newId): array
    {
        // Validate the new ID
        if ($this->validateNewId($student, $newId) !== true) {
            return $this->validateNewId($student, $newId);
        }

        $oldId = $student->id;
        
        DB::beginTransaction();
        
        try {
            // Update all related records first (before updating the student)
            $this->updateRelatedRecords($oldId, $newId);
            
            // Update the student record itself
            $student->id = $newId;
            $student->save();
            
            DB::commit();
            
            Log::info("Student ID updated successfully", [
                'old_id' => $oldId,
                'new_id' => $newId,
                'student_name' => $student->full_name
            ]);
            
            return [
                'success' => true,
                'message' => "Student ID successfully updated from {$oldId} to {$newId}",
                'old_id' => $oldId,
                'new_id' => $newId
            ];
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error("Failed to update student ID", [
                'old_id' => $oldId,
                'new_id' => $newId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => "Failed to update student ID: " . $e->getMessage(),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Validate the new ID
     *
     * @param Student $student
     * @param int $newId
     * @return array|true
     */
    private function validateNewId(Student $student, int $newId): array|true
    {
        // Check if new ID is the same as current
        if ($student->id === $newId) {
            return [
                'success' => false,
                'message' => 'New ID cannot be the same as the current ID'
            ];
        }

        // Check if new ID already exists
        if (Student::where('id', $newId)->exists()) {
            return [
                'success' => false,
                'message' => "Student ID {$newId} already exists"
            ];
        }

        // Validate ID format (assuming positive integers)
        if ($newId <= 0) {
            return [
                'success' => false,
                'message' => 'Student ID must be a positive integer'
            ];
        }

        return true;
    }

    /**
     * Update all records that reference the student ID
     *
     * @param int $oldId
     * @param int $newId
     * @return void
     */
    private function updateRelatedRecords(int $oldId, int $newId): void
    {
        // Update StudentTuition records
        StudentTuition::where('student_id', $oldId)->update(['student_id' => $newId]);
        
        // Update StudentTransaction records
        StudentTransaction::where('student_id', $oldId)->update(['student_id' => $newId]);
        
        // Update StudentEnrollment records
        StudentEnrollment::where('student_id', $oldId)->update(['student_id' => $newId]);
        
        // Update ClassEnrollment records
        ClassEnrollment::where('student_id', $oldId)->update(['student_id' => $newId]);
        
        // Update SubjectEnrollment records
        SubjectEnrollment::where('student_id', $oldId)->update(['student_id' => $newId]);
        
        // Update Account records (if they reference student by person_id)
        Account::where('person_id', $oldId)->update(['person_id' => $newId]);
    }

    /**
     * Get a summary of records that will be affected by the ID change
     *
     * @param int $studentId
     * @return array
     */
    public function getAffectedRecordsSummary(int $studentId): array
    {
        return [
            'student_tuitions' => StudentTuition::where('student_id', $studentId)->count(),
            'student_transactions' => StudentTransaction::where('student_id', $studentId)->count(),
            'student_enrollments' => StudentEnrollment::where('student_id', $studentId)->count(),
            'class_enrollments' => ClassEnrollment::where('student_id', $studentId)->count(),
            'subject_enrollments' => SubjectEnrollment::where('student_id', $studentId)->count(),
            'accounts' => Account::where('person_id', $studentId)->count(),
        ];
    }

    /**
     * Check if a student ID is available
     *
     * @param int $id
     * @return bool
     */
    public function isIdAvailable(int $id): bool
    {
        return !Student::where('id', $id)->exists();
    }

    /**
     * Generate a suggested new ID based on existing patterns
     *
     * @return int
     */
    public function generateSuggestedId(): int
    {
        // Get the highest existing ID and add 1
        $maxId = Student::max('id') ?? 0;
        return $maxId + 1;
    }

    /**
     * Perform a dry run to check what would be updated
     *
     * @param Student $student
     * @param int $newId
     * @return array
     */
    public function dryRun(Student $student, int $newId): array
    {
        $validation = $this->validateNewId($student, $newId);
        if ($validation !== true) {
            return $validation;
        }

        $summary = $this->getAffectedRecordsSummary($student->id);
        $totalRecords = array_sum($summary);

        return [
            'success' => true,
            'message' => "Dry run successful. {$totalRecords} records would be updated.",
            'affected_records' => $summary,
            'total_records' => $totalRecords,
            'old_id' => $student->id,
            'new_id' => $newId
        ];
    }
}
