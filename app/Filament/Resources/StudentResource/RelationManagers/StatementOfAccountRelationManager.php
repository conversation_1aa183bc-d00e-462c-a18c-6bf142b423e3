<?php


namespace App\Filament\Resources\StudentResource\RelationManagers;

use App\Services\StudentIdUpdateService;
use Filament\Forms;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Support\Enums\FontWeight;
use Filament\Tables;
use Filament\Tables\Table;

final class StatementOfAccountRelationManager extends RelationManager
{
    protected static string $relationship = 'StudentTuition';

    protected static bool $isLazy = false;

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('school_year')
            ->heading('Statement of Account')
            ->description('Detailed breakdown of tuition fees and payments')
            ->columns([
                Tables\Columns\TextColumn::make('school_year')
                    ->label('Academic Year')
                    ->badge()
                    ->color('primary')
                    ->sortable(),

                Tables\Columns\TextColumn::make('semester')
                    ->formatStateUsing(
                        fn ($state): string => match ($state) {
                            1 => '1st Semester',
                            2 => '2nd Semester',
                            default => 'Summer',
                        }
                    )
                    // ->badge()
                    ->color('gray'),

                Tables\Columns\TextColumn::make('overall_tuition')
                    ->label('Total Assessment')
                    ->money('PHP')
                    ->weight(FontWeight::Bold)
                    ->color('primary'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Assessment Date')
                    ->date('M d, Y')
                    ->color('gray'),

                Tables\Columns\TextColumn::make('total_balance')
                    ->label('Remaining Balance')
                    ->money('PHP')
                    ->color(
                        fn ($record): string => $record->total_balance > 0
                            ? 'danger'
                            : 'success'
                    ),
            ])

            ->headerActions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\CreateAction::make()
                        ->label('Create New Assessment')
                        ->modalHeading('Create New Tuition Assessment')
                        ->form([
                            Forms\Components\Select::make('semester')
                                ->options([
                                    1 => '1st Semester',
                                    2 => '2nd Semester',
                                    3 => 'Summer',
                                ])
                                ->required(),

                            Forms\Components\Select::make('school_year')
                                ->options(function (): array {
                                    $years = [];
                                    for (
                                        $i = date('Y') - 5;
                                        $i <= date('Y') + 5;
                                        $i++
                                    ) {
                                        $years["$i - ".($i + 1)] =
                                            "$i - ".($i + 1);
                                    }

                                    return $years;
                                })
                                ->required(),

                            Forms\Components\TextInput::make('total_tuition')
                                ->numeric()
                                ->prefix('₱')
                                ->required(),

                            Forms\Components\TextInput::make('total_lectures')
                                ->numeric()
                                ->prefix('₱')
                                ->required(),

                            Forms\Components\TextInput::make('total_laboratory')
                                ->numeric()
                                ->prefix('₱')
                                ->required(),

                            Forms\Components\TextInput::make(
                                'total_miscelaneous_fees'
                            )
                                ->label('Miscellaneous Fees')
                                ->numeric()
                                ->prefix('₱')
                                ->required(),
                        ]),
                ])
                    ->label('Assessment Actions')
                    ->icon('heroicon-o-document-text')
                    ->color('primary'),

                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('updateStudentId')
                        ->label('Update Student ID')
                        ->icon('heroicon-o-identification')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->modalHeading('Update Student ID')
                        ->modalDescription('This will update the student ID and all related records in the database. This action cannot be undone.')
                        ->form([
                            Forms\Components\Section::make('Current Information')
                                ->schema([
                                    Forms\Components\Placeholder::make('current_id')
                                        ->label('Current Student ID')
                                        ->content(fn () => $this->getOwnerRecord()->id),

                                    Forms\Components\Placeholder::make('student_name')
                                        ->label('Student Name')
                                        ->content(fn () => $this->getOwnerRecord()->full_name),
                                ])
                                ->columns(2),

                            Forms\Components\Section::make('Impact Summary')
                                ->schema([
                                    Forms\Components\Placeholder::make('affected_records')
                                        ->label('Records that will be updated')
                                        ->content(function () {
                                            $service = app(StudentIdUpdateService::class);
                                            $summary = $service->getAffectedRecordsSummary($this->getOwnerRecord()->id);

                                            $content = '';
                                            foreach ($summary as $table => $count) {
                                                if ($count > 0) {
                                                    $tableName = str_replace('_', ' ', ucfirst($table));
                                                    $content .= "• {$tableName}: {$count} record(s)\n";
                                                }
                                            }

                                            return $content ?: 'No related records found.';
                                        })
                                        ->columnSpanFull(),
                                ]),

                            Forms\Components\Section::make('New ID')
                                ->schema([
                                    Forms\Components\TextInput::make('new_student_id')
                                        ->label('New Student ID')
                                        ->numeric()
                                        ->required()
                                        ->rules([
                                            'integer',
                                            'min:1',
                                            function ($attribute, $value, \Closure $fail) {
                                                if ($value == $this->getOwnerRecord()->id) {
                                                    $fail('New ID cannot be the same as current ID.');
                                                }

                                                $service = app(StudentIdUpdateService::class);
                                                if (!$service->isIdAvailable($value)) {
                                                    $fail("Student ID {$value} already exists.");
                                                }
                                            },
                                        ])
                                        ->helperText(function () {
                                            $service = app(StudentIdUpdateService::class);
                                            $suggested = $service->generateSuggestedId();
                                            return "Suggested ID: {$suggested}";
                                        }),
                                ]),
                        ])
                        ->action(function (array $data): void {
                            $service = app(StudentIdUpdateService::class);
                            $student = $this->getOwnerRecord();

                            $result = $service->updateStudentId($student, (int) $data['new_student_id']);

                            if ($result['success']) {
                                Notification::make()
                                    ->title('Student ID Updated Successfully')
                                    ->body($result['message'])
                                    ->success()
                                    ->send();

                                // Redirect to the student with the new ID
                                redirect()->to(
                                    route('filament.admin.resources.students.view', ['record' => $result['new_id']])
                                );
                            } else {
                                Notification::make()
                                    ->title('Failed to Update Student ID')
                                    ->body($result['message'])
                                    ->danger()
                                    ->send();
                            }
                        }),
                ])
                    ->label('Administrative Actions')
                    ->icon('heroicon-o-cog-6-tooth')
                    ->color('gray'),
            ])
            ->actions([
                Tables\Actions\Action::make('payments')
                    ->label('View Payments')
                    ->icon('heroicon-o-currency-dollar')
                    ->modalHeading('Payment Transactions')
                    ->modalSubmitAction(false)
                    ->modalCancelAction(false)
                    ->infolist([
                        Section::make('Payment History')->schema([
                            RepeatableEntry::make('studentTransactions')
                                ->label(false)
                                ->schema([
                                    Grid::make(4)->schema([
                                        TextEntry::make(
                                            'transaction.transaction_date'
                                        )
                                            ->label('Date')
                                            ->date('M d, Y'),

                                        TextEntry::make(
                                            'transaction.invoicenumber'
                                        )
                                            ->label('Invoice No.')
                                            ->copyable()
                                            ->badge()
                                            ->color('primary'),

                                        TextEntry::make(
                                            'transaction.total_amount'
                                        )
                                            ->label('Amount Paid (Settled)')
                                            ->money('PHP'),

                                        TextEntry::make('status')
                                            ->label('Status')
                                            ->badge()
                                            ->color(
                                                fn (
                                                    string $state
                                                ): string => match ($state) {
                                                    'Paid' => 'success',
                                                    'Pending' => 'warning',
                                                    'Failed' => 'danger',
                                                    default => 'gray',
                                                }
                                            ),
                                    ]),
                                    TextEntry::make('transaction.description')
                                        ->label('Description')
                                        ->columnSpanFull(),
                                ])
                                ->columns(1)
                                ->default(
                                    fn ($record) => $record->studentTransactions
                                ),
                        ]),
                    ]),

                Tables\Actions\EditAction::make()->form([
                    Forms\Components\TextInput::make('total_balance')
                        ->numeric()
                        ->prefix('₱')
                        ->required(),
                    Forms\Components\TextInput::make('overall_tuition')
                        ->numeric()
                        ->prefix('₱')
                        ->required(),

                    Forms\Components\Select::make('status')
                        ->options([
                            'paid' => 'Paid',
                            'partial' => 'Partial Payment',
                            'unpaid' => 'Unpaid',
                        ])
                        ->required(),
                ]),

                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateHeading('No tuition assessments found')
            ->emptyStateDescription(
                'Click the button below to create a new assessment'
            )
            ->emptyStateIcon('heroicon-o-document-text')
            ->emptyStateActions([
                Tables\Actions\CreateAction::make()
                    ->label('Create Assessment')
                    ->button(),
            ]);
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist->schema([
            Section::make('Fee Breakdown')->schema([
                Grid::make(3)->schema([
                    TextEntry::make('total_lectures')
                        ->label('Lecture Fees')
                        ->money('PHP'),

                    TextEntry::make('total_laboratory')
                        ->label('Lab Fees')
                        ->money('PHP'),

                    TextEntry::make('total_miscelaneous_fees')
                        ->label('Miscellaneous')
                        ->money('PHP'),
                ]),

                Grid::make(2)->schema([
                    TextEntry::make('total_tuition')
                        ->label('Total Assessment')
                        ->money('PHP')
                        ->weight(FontWeight::Bold),

                    TextEntry::make('total_balance')
                        ->label('Remaining Balance')
                        ->money('PHP')
                        ->color(
                            fn ($state): string => $state > 0
                                ? 'danger'
                                : 'success'
                        ),
                ]),
            ]),

            Section::make('Payment History')
                ->description(
                    'List of all transactions related to this assessment'
                )
                ->schema([
                    RepeatableEntry::make('studentTransactions')
                        ->schema([
                            Grid::make(4)->schema([
                                TextEntry::make('transaction_date')->date(
                                    'M d, Y'
                                ),

                                TextEntry::make('transaction_number')->label(
                                    'Transaction No'
                                ),

                                TextEntry::make('amount')->money('PHP'),

                                TextEntry::make('status')->badge()->color(
                                    fn ($state): string => match ($state) {
                                        'paid' => 'success',
                                        'pending' => 'warning',
                                        'failed' => 'danger',
                                        default => 'gray',
                                    }
                                ),

                                TextEntry::make('remarks')->limit(30),
                            ]),
                        ])
                        ->columns(1),
                ]),
        ]);
    }
}
